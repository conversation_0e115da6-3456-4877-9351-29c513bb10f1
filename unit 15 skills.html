<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Speed Reading Comprehension App</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            background: #8e44ad;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .text-container {
            background: #fff;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 20px;
            min-height: 300px;
        }
        
        .highlight {
            background-color: #87CEEB;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.1s ease;
        }
        
        .question {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #8e44ad;
        }
        
        .options {
            margin-top: 15px;
        }
        
        .option {
            display: block;
            margin: 8px 0;
            padding: 10px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            border-color: #8e44ad;
            transform: translateX(5px);
        }
        
        .option.selected {
            background: #e8f4fd;
            border-color: #3498db;
        }
        
        .option.correct {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .option.incorrect {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .btn {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #732d91;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .word-box {
            background: #e8f4fd;
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .word-bank {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .word-item {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .word-item:hover {
            background: #2980b9;
            transform: scale(1.05);
        }
        
        .gap {
            background: #fff3cd;
            border: 2px dashed #ffc107;
            padding: 5px 10px;
            margin: 0 2px;
            cursor: pointer;
            border-radius: 5px;
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }
        
        .gap.revealed {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
        }
        
        .puzzle-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .puzzle-level {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #ddd;
        }
        
        .drag-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .sentence-parts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 400px;
        }
        
        .sentence-item {
            background: #3498db;
            color: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            transition: all 0.3s ease;
        }
        
        .sentence-item:hover {
            background: #2980b9;
            transform: scale(1.02);
        }
        
        .sentence-item.dragging {
            opacity: 0.5;
        }
        
        .drop-zone {
            background: #fff;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            min-height: 60px;
            transition: all 0.3s ease;
        }
        
        .drop-zone.drag-over {
            border-color: #8e44ad;
            background: #f8f4fd;
        }
        
        .navigation {
            text-align: center;
            margin-top: 30px;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #8e44ad, #3498db);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .score-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #8e44ad;
            margin: 20px 0;
        }
        
        input, select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #8e44ad;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Speed Reading Adventure!</h1>
            <p>Follow the highlight and test your comprehension!</p>
        </div>

        <!-- Speed Reading Section -->
        <div id="speed-reading" class="section active">
            <h2>📖 Speed Reading Practice</h2>
            <div class="controls">
                <label>Words per minute: <input type="number" id="wpm" value="200" min="50" max="800"></label>
                <label>Highlight: 
                    <select id="highlight-mode">
                        <option value="1">1 word</option>
                        <option value="2">2 words</option>
                        <option value="3">3 words</option>
                        <option value="sentence">Full sentence</option>
                    </select>
                </label>
                <button class="btn" onclick="startReading()">▶️ Start</button>
                <button class="btn" onclick="pauseReading()">⏸️ Pause</button>
                <button class="btn" onclick="resetReading()">🔄 Reset</button>
            </div>
            <div class="text-container" id="reading-text"></div>
            <div class="navigation">
                <button class="btn" onclick="showSection('comprehension')">Next: Comprehension Questions →</button>
            </div>
        </div>

        <!-- Comprehension Questions Section -->
        <div id="comprehension" class="section">
            <h2>🧠 Comprehension Questions</h2>
            <div id="questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showSection('gap-fill')">Next: Gap Fill →</button>
            </div>
            <div class="score-display" id="comprehension-score"></div>
        </div>

        <!-- Gap Fill Section -->
        <div id="gap-fill" class="section">
            <h2>🔤 Fill in the Gaps</h2>
            <div class="word-box">
                <h3>Word Bank</h3>
                <div class="word-bank" id="word-bank"></div>
            </div>
            <div class="text-container" id="gap-text"></div>
            <div class="navigation">
                <button class="btn" onclick="showSection('puzzle')">Next: Holiday Puzzle →</button>
            </div>
        </div>

        <!-- Holiday Puzzle Section -->
        <div id="puzzle" class="section">
            <h2>🏖️ Holiday Activities Puzzle</h2>
            <div class="puzzle-grid" id="puzzle-container"></div>
            <div class="navigation">
                <button class="btn" onclick="showSection('sentence-order')">Next: Sentence Order →</button>
            </div>
        </div>

        <!-- Sentence Ordering Section -->
        <div id="sentence-order" class="section">
            <h2>📝 Put Sentences in Order</h2>
            <div class="drag-container">
                <div class="sentence-parts">
                    <h3>Drag from here:</h3>
                    <div id="sentence-source"></div>
                </div>
                <div class="sentence-parts">
                    <h3>Drop in correct order:</h3>
                    <div id="sentence-target"></div>
                </div>
            </div>
            <div class="navigation">
                <button class="btn" onclick="checkOrder()">✅ Check Order</button>
                <button class="btn" onclick="showSection('future-quiz')">Next: Future Tense Quiz →</button>
            </div>
        </div>

        <!-- Future Tense Quiz Section -->
        <div id="future-quiz" class="section">
            <h2>🔮 Future Plans Quiz</h2>
            <div id="future-questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkFutureAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showFutureAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showFinalResults()">🎉 Show Final Results</button>
            </div>
            <div class="score-display" id="future-score"></div>
        </div>

        <!-- Final Results -->
        <div id="final-results" class="section">
            <h2>🎊 Congratulations!</h2>
            <div class="score-display" id="final-score-display"></div>
            <div class="navigation">
                <button class="btn" onclick="restartApp()">🔄 Start Over</button>
            </div>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progress-bar" style="width: 14.28%"></div>
        </div>
    </div>

    <script>
        // Text content from the image
        const fullText = `Hi Dan,
How are you? I'm having a fantastic time in Barcelona! The weather here is great! It's very hot and sunny.
We went to the amusement park yesterday. I went on all the rides! It was brilliant!
This afternoon, we're going to visit the aquarium. There are lots of amazing sea creatures there. I can't wait to see the sharks! My sister is scared of them, but I'm not. We're going to catch a dolphin show. I love dolphins!
We're going to have dinner in a restaurant tonight. I want to try seafood!
We're going to visit Montjuic Castle tomorrow. Then we're going to the beach. My sister wants to windsurf but I want to swim in the sea.
We're going to fly home next week. I'm going to have lots more cool photos to show you!
See you soon.
Best wishes,
William`;

        // Global variables
        let readingTimer;
        let currentWordIndex = 0;
        let words = [];
        let sentences = [];
        let isReading = false;
        let comprehensionAnswers = [];
        let futureAnswers = [];
        let userScores = {
            comprehension: 0,
            future: 0,
            total: 0
        };

        // Initialize the app
        function initializeApp() {
            setupSpeedReading();
            setupComprehensionQuestions();
            setupGapFill();
            setupHolidayPuzzle();
            setupSentenceOrder();
            setupFutureQuiz();
        }

        // Speed Reading Functions
        function setupSpeedReading() {
            words = fullText.split(/\s+/);
            sentences = fullText.split(/[.!?]+/).filter(s => s.trim().length > 0);
            document.getElementById('reading-text').innerHTML = fullText;
        }

        function startReading() {
            if (isReading) return;
            
            isReading = true;
            const wpm = parseInt(document.getElementById('wpm').value);
            const highlightMode = document.getElementById('highlight-mode').value;
            const interval = 60000 / wpm; // milliseconds per word
            
            readingTimer = setInterval(() => {
                highlightWords(highlightMode);
            }, interval);
        }

        function highlightWords(mode) {
            const textContainer = document.getElementById('reading-text');
            let wordsToHighlight = 1;
            
            if (mode === 'sentence') {
                // Find current sentence
                let wordCount = 0;
                for (let i = 0; i < sentences.length; i++) {
                    const sentenceWords = sentences[i].trim().split(/\s+/).length;
                    if (currentWordIndex < wordCount + sentenceWords) {
                        highlightSentence(i);
                        currentWordIndex = wordCount + sentenceWords;
                        break;
                    }
                    wordCount += sentenceWords;
                }
            } else {
                wordsToHighlight = parseInt(mode);
                highlightWordGroup(wordsToHighlight);
            }
            
            if (currentWordIndex >= words.length) {
                pauseReading();
            }
        }

        function highlightWordGroup(count) {
            const textContainer = document.getElementById('reading-text');
            let html = '';
            let wordIndex = 0;
            
            const wordsArray = fullText.split(/(\s+)/);
            
            for (let i = 0; i < wordsArray.length; i++) {
                if (wordsArray[i].trim().length > 0) {
                    if (wordIndex >= currentWordIndex && wordIndex < currentWordIndex + count) {
                        html += `<span class="highlight">${wordsArray[i]}</span>`;
                    } else {
                        html += wordsArray[i];
                    }
                    wordIndex++;
                } else {
                    html += wordsArray[i];
                }
            }
            
            textContainer.innerHTML = html;
            currentWordIndex += count;
        }

        function highlightSentence(sentenceIndex) {
            const textContainer = document.getElementById('reading-text');
            let html = fullText;
            
            if (sentences[sentenceIndex]) {
                const sentence = sentences[sentenceIndex].trim();
                html = html.replace(sentence, `<span class="highlight">${sentence}</span>`);
            }
            
            textContainer.innerHTML = html;
        }

        function pauseReading() {
            isReading = false;
            if (readingTimer) {
                clearInterval(readingTimer);
            }
        }

        function resetReading() {
            pauseReading();
            currentWordIndex = 0;
            document.getElementById('reading-text').innerHTML = fullText;
        }

        // Comprehension Questions
        function setupComprehensionQuestions() {
            const questions = [
                {
                    question: "Where is William spending his holiday?",
                    options: ["Madrid", "Barcelona", "Valencia", "Seville"],
                    correct: 1
                },
                {
                    question: "What is the weather like in Barcelona?",
                    options: ["Cold and rainy", "Cool and cloudy", "Hot and sunny", "Warm and windy"],
                    correct: 2
                },
                {
                    question: "What did William do yesterday?",
                    options: ["Visited the aquarium", "Went to the beach", "Went to an amusement park", "Visited a castle"],
                    correct: 2
                },
                {
                    question: "What are they planning to visit this afternoon?",
                    options: ["The beach", "The aquarium", "Montjuic Castle", "A restaurant"],
                    correct: 1
                },
                {
                    question: "Who is scared of sharks?",
                    options: ["William", "Dan", "William's sister", "Nobody"],
                    correct: 2
                },
                {
                    question: "What show are they going to catch?",
                    options: ["Shark show", "Dolphin show", "Fish show", "Whale show"],
                    correct: 1
                },
                {
                    question: "Where are they having dinner tonight?",
                    options: ["At the hotel", "At the beach", "In a restaurant", "At home"],
                    correct: 2
                },
                {
                    question: "What does William want to try for dinner?",
                    options: ["Pizza", "Seafood", "Pasta", "Chicken"],
                    correct: 1
                },
                {
                    question: "What are they visiting tomorrow?",
                    options: ["The aquarium", "Montjuic Castle", "The amusement park", "A restaurant"],
                    correct: 1
                },
                {
                    question: "What does William's sister want to do at the beach?",
                    options: ["Swim", "Surf", "Windsurf", "Sunbathe"],
                    correct: 2
                },
                {
                    question: "When are they flying home?",
                    options: ["Tomorrow", "Tonight", "Next week", "This afternoon"],
                    correct: 2
                },
                {
                    question: "What does William want to show Dan?",
                    options: ["Souvenirs", "Photos", "Postcards", "Videos"],
                    correct: 1
                }
            ];

            const container = document.getElementById('questions-container');
            questions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectOption(${index}, ${optIndex})">
                                <input type="radio" name="q${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            comprehensionAnswers = questions.map(q => q.correct);
        }

        function selectOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="q${questionIndex}"]`);
            options.forEach(opt => opt.checked = false);
            options[optionIndex].checked = true;
            
            const labels = document.querySelectorAll(`input[name="q${questionIndex}"]`).forEach((input, idx) => {
                input.parentElement.classList.remove('selected');
            });
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkAnswers() {
            let correct = 0;
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const selected = document.querySelector(`input[name="q${i}"]:checked`);
                if (selected && parseInt(selected.value) === comprehensionAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }
            
            userScores.comprehension = Math.round((correct / comprehensionAnswers.length) * 100);
            document.getElementById('comprehension-score').textContent = 
                `Score: ${correct}/${comprehensionAnswers.length} (${userScores.comprehension}%)`;
        }

        function showAnswers() {
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="q${i}"][value="${comprehensionAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Gap Fill Functions
        function setupGapFill() {
            const gapWords = ['fantastic', 'weather', 'yesterday', 'afternoon', 'amazing', 'scared', 'tonight', 'tomorrow', 'beach', 'swim', 'week', 'photos'];
            const wordBank = document.getElementById('word-bank');
            
            gapWords.forEach(word => {
                const wordElement = document.createElement('span');
                wordElement.className = 'word-item';
                wordElement.textContent = word;
                wordBank.appendChild(wordElement);
            });

            let gapText = fullText;
            gapWords.forEach((word, index) => {
                gapText = gapText.replace(new RegExp(`\\b${word}\\b`, 'i'), 
                    `<span class="gap" onclick="revealGap(${index})">${index + 1}</span>`);
            });

            document.getElementById('gap-text').innerHTML = gapText;
            window.gapAnswers = gapWords;
        }

        function revealGap(index) {
            const gaps = document.querySelectorAll('.gap');
            if (gaps[index]) {
                gaps[index].classList.add('revealed');
                gaps[index].textContent = window.gapAnswers[index];
            }
        }

        // Holiday Puzzle Functions
        function setupHolidayPuzzle() {
            const puzzles = [
                {
                    level: "Level 1 - Easy",
                    question: "William wants to see sea creatures. Where should he go?",
                    options: ["Restaurant", "Aquarium", "Castle", "Beach"],
                    correct: 1
                },
                {
                    level: "Level 2 - Medium", 
                    question: "If William goes to the beach tomorrow and his sister wants to windsurf, what equipment does she need?",
                    options: ["Swimming goggles", "Surfboard and sail", "Fishing rod", "Beach ball"],
                    correct: 1
                },
                {
                    level: "Level 3 - Hard",
                    question: "William's schedule: Aquarium (afternoon), Restaurant (tonight), Castle (tomorrow morning), Beach (tomorrow afternoon). In what order will he visit these places?",
                    options: ["Castle, Aquarium, Beach, Restaurant", "Aquarium, Restaurant, Castle, Beach", "Restaurant, Castle, Aquarium, Beach", "Beach, Castle, Restaurant, Aquarium"],
                    correct: 1
                }
            ];

            const container = document.getElementById('puzzle-container');
            puzzles.forEach((puzzle, index) => {
                const puzzleDiv = document.createElement('div');
                puzzleDiv.className = 'puzzle-level';
                puzzleDiv.innerHTML = `
                    <h3>${puzzle.level}</h3>
                    <p><strong>${puzzle.question}</strong></p>
                    <div class="options">
                        ${puzzle.options.map((option, optIndex) => `
                            <label class="option" onclick="selectPuzzleOption(${index}, ${optIndex})">
                                <input type="radio" name="puzzle${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(puzzleDiv);
            });
        }

        function selectPuzzleOption(puzzleIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="puzzle${puzzleIndex}"]`);
            options.forEach(opt => {
                opt.checked = false;
                opt.parentElement.classList.remove('selected');
            });
            options[optionIndex].checked = true;
            options[optionIndex].parentElement.classList.add('selected');
        }

        // Sentence Ordering Functions
        function setupSentenceOrder() {
            const sentenceParts = [
                "Hi Dan, How are you?",
                "I'm having a fantastic time in Barcelona!",
                "We went to the amusement park yesterday.",
                "This afternoon, we're going to visit the aquarium.",
                "We're going to have dinner in a restaurant tonight.",
                "We're going to visit Montjuic Castle tomorrow.",
                "We're going to fly home next week.",
                "Best wishes, William"
            ];

            const shuffled = [...sentenceParts].sort(() => Math.random() - 0.5);
            const sourceContainer = document.getElementById('sentence-source');
            const targetContainer = document.getElementById('sentence-target');

            shuffled.forEach((sentence, index) => {
                const sentenceElement = document.createElement('div');
                sentenceElement.className = 'sentence-item';
                sentenceElement.textContent = sentence;
                sentenceElement.draggable = true;
                sentenceElement.dataset.original = sentenceParts.indexOf(sentence);
                
                sentenceElement.addEventListener('dragstart', handleDragStart);
                sentenceElement.addEventListener('dragend', handleDragEnd);
                
                sourceContainer.appendChild(sentenceElement);
            });

            // Create drop zones
            for (let i = 0; i < 8; i++) {
                const dropZone = document.createElement('div');
                dropZone.className = 'drop-zone';
                dropZone.textContent = `Position ${i + 1}`;
                dropZone.dataset.position = i;
                
                dropZone.addEventListener('dragover', handleDragOver);
                dropZone.addEventListener('drop', handleDrop);
                dropZone.addEventListener('dragleave', handleDragLeave);
                
                targetContainer.appendChild(dropZone);
            }

            window.correctOrder = sentenceParts;
        }

        function handleDragStart(e) {
            e.target.classList.add('dragging');
            e.dataTransfer.setData('text/plain', e.target.textContent);
            e.dataTransfer.setData('application/json', JSON.stringify({
                text: e.target.textContent,
                original: e.target.dataset.original
            }));
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');
            
            const data = JSON.parse(e.dataTransfer.getData('application/json'));
            e.target.textContent = data.text;
            e.target.dataset.original = data.original;
            e.target.style.background = '#e8f4fd';
        }

        function checkOrder() {
            const dropZones = document.querySelectorAll('.drop-zone');
            let correct = 0;
            
            dropZones.forEach((zone, index) => {
                if (zone.dataset.original == index) {
                    zone.style.background = '#d4edda';
                    correct++;
                } else {
                    zone.style.background = '#f8d7da';
                }
            });
            
            alert(`You got ${correct}/8 sentences in the correct order!`);
        }

        // Future Tense Quiz Functions
        function setupFutureQuiz() {
            const futureQuestions = [
                {
                    question: "Complete: William is _____ to visit the aquarium this afternoon.",
                    options: ["go", "going", "goes", "went"],
                    correct: 1
                },
                {
                    question: "When will William fly home?",
                    options: ["tomorrow", "tonight", "next week", "yesterday"],
                    correct: 2
                },
                {
                    question: "What does 'We're going to have dinner' mean?",
                    options: ["We had dinner", "We are having dinner now", "We will have dinner", "We never have dinner"],
                    correct: 2
                },
                {
                    question: "Complete: _____ we're going to the beach.",
                    options: ["Yesterday", "Tomorrow", "Last week", "Last month"],
                    correct: 1
                },
                {
                    question: "Which time word shows future plans?",
                    options: ["yesterday", "soon", "last week", "ago"],
                    correct: 1
                },
                {
                    question: "Complete: I want to try seafood _____.",
                    options: ["yesterday", "last week", "tonight", "ago"],
                    correct: 2
                },
                {
                    question: "What is William going to do next week?",
                    options: ["Visit the castle", "Go to the aquarium", "Fly home", "Go to the beach"],
                    correct: 2
                },
                {
                    question: "Complete: My sister _____ to windsurf.",
                    options: ["want", "wants", "wanted", "wanting"],
                    correct: 1
                },
                {
                    question: "Which sentence shows a future plan?",
                    options: ["We went yesterday", "We are going tomorrow", "We go every day", "We went last week"],
                    correct: 1
                },
                {
                    question: "Complete: _____ I'm going to have lots of photos.",
                    options: ["Yesterday", "Last week", "Soon", "Ago"],
                    correct: 2
                },
                {
                    question: "What does 'this afternoon' refer to?",
                    options: ["Past time", "Future time today", "Next week", "Last week"],
                    correct: 1
                },
                {
                    question: "Complete: We're _____ to visit Montjuic Castle.",
                    options: ["go", "going", "went", "goes"],
                    correct: 1
                },
                {
                    question: "Which word shows immediate future?",
                    options: ["tomorrow", "next week", "later", "soon"],
                    correct: 3
                },
                {
                    question: "Complete: I _____ to see the sharks!",
                    options: ["can't wait", "couldn't wait", "don't wait", "didn't wait"],
                    correct: 0
                },
                {
                    question: "What time expression shows future plans in the text?",
                    options: ["yesterday", "this afternoon", "last week", "ago"],
                    correct: 1
                }
            ];

            const container = document.getElementById('future-questions-container');
            futureQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectFutureOption(${index}, ${optIndex})">
                                <input type="radio" name="future${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            futureAnswers = futureQuestions.map(q => q.correct);
        }

        function selectFutureOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="future${questionIndex}"]`);
            options.forEach(opt => {
                opt.checked = false;
                opt.parentElement.classList.remove('selected');
            });
            options[optionIndex].checked = true;
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkFutureAnswers() {
            let correct = 0;
            for (let i = 0; i < futureAnswers.length; i++) {
                const selected = document.querySelector(`input[name="future${i}"]:checked`);
                if (selected && parseInt(selected.value) === futureAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }
            
            userScores.future = Math.round((correct / futureAnswers.length) * 100);
            document.getElementById('future-score').textContent = 
                `Score: ${correct}/${futureAnswers.length} (${userScores.future}%)`;
        }

        function showFutureAnswers() {
            for (let i = 0; i < futureAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="future${i}"][value="${futureAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Navigation and Progress Functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Update progress bar
            const sections = ['speed-reading', 'comprehension', 'gap-fill', 'puzzle', 'sentence-order', 'future-quiz', 'final-results'];
            const currentIndex = sections.indexOf(sectionId);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progress-bar').style.width = progress + '%';
        }

        function showFinalResults() {
            userScores.total = Math.round((userScores.comprehension + userScores.future) / 2);
            
            let message = '';
            if (userScores.total >= 90) {
                message = '🌟 Excellent! You are a reading superstar!';
            } else if (userScores.total >= 80) {
                message = '🎉 Great job! You have strong reading skills!';
            } else if (userScores.total >= 70) {
                message = '👍 Good work! Keep practicing to improve!';
            } else {
                message = '📚 Keep reading and practicing! You can do it!';
            }

            document.getElementById('final-score-display').innerHTML = `
                <h3>${message}</h3>
                <p>Comprehension Score: ${userScores.comprehension}%</p>
                <p>Future Tense Score: ${userScores.future}%</p>
                <p><strong>Overall Score: ${userScores.total}%</strong></p>
            `;
            
            showSection('final-results');
        }

        function restartApp() {
            // Reset all scores
            userScores = { comprehension: 0, future: 0, total: 0 };
            
            // Reset reading
            resetReading();
            
            // Clear all selections
            document.querySelectorAll('input[type="radio"]').forEach(input => {
                input.checked = false;
                input.parentElement.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // Reset gap fill
            document.querySelectorAll('.gap').forEach(gap => {
                gap.classList.remove('revealed');
                const index = parseInt(gap.textContent) - 1;
                if (!isNaN(index)) {
                    gap.textContent = index + 1;
                }
            });
            
            // Reset sentence order
            const sourceContainer = document.getElementById('sentence-source');
            const targetContainer = document.getElementById('sentence-target');
            const dropZones = document.querySelectorAll('.drop-zone');
            
            dropZones.forEach((zone, index) => {
                if (zone.dataset.original !== undefined) {
                    const sentenceElement = document.createElement('div');
                    sentenceElement.className = 'sentence-item';
                    sentenceElement.textContent = zone.textContent;
                    sentenceElement.draggable = true;
                    sentenceElement.dataset.original = zone.dataset.original;
                    
                    sentenceElement.addEventListener('dragstart', handleDragStart);
                    sentenceElement.addEventListener('dragend', handleDragEnd);
                    
                    sourceContainer.appendChild(sentenceElement);
                }
                
                zone.textContent = `Position ${index + 1}`;
                zone.style.background = '';
                delete zone.dataset.original;
            });
            
            // Clear scores
            document.getElementById('comprehension-score').textContent = '';
            document.getElementById('future-score').textContent = '';
            
            // Go back to first section
            showSection('speed-reading');
        }

        // Initialize the app when page loads
        window.onload = function() {
            initializeApp();
        };
    </script>
</body>
</html>